<template>
  <qz-dialog :title="title" :visible.sync="visible" top="6vh" width="600px">
    <div class="KS-Form">
      <el-form ref="dataForm" :model="dataForm" label-width="80px" size="small">
        <el-form-item label="已选诊断" prop="choosedDiagnosis">
          <div class="tag-container">
            <el-tag v-for="(diagnosis, index) in choosedDiagnosisShow" :key="index" closable
              @close="handleRemoveTag(index)" style="margin: 2px 5px 2px 0;">
              {{ diagnosis }}
            </el-tag>
            <div v-if="choosedDiagnosisShow.length === 0" class="el-form-item__error">
              请选择诊断
            </div>
          </div>
        </el-form-item>
        <el-form-item label="输入诊断" prop="diagnosis">
          <el-input placeholder="输入关键字过滤诊断" v-model="filterTextDiagnosis" size="small">
            <i @click="initLeftDiagnosisTreeData" class="el-input__icon el-icon-search" slot="suffix"
              style="cursor: pointer"></i>
          </el-input>
          <el-tree class="filter-tree-dept" :data="dataDiagnosis" show-checkbox node-key="id" :props="defaultProps"
            default-expand-all style="max-height: 300px;margin-top: 5px;" :filter-node-method="filterNodeDiagnosis"
            ref="treeDiagnosis" :expand-on-click-node="false" :highlight-current="true"
            @check-change="handleCheckChange">
          </el-tree>
        </el-form-item>
      </el-form>
    </div>
    <template slot="footer">
      <el-button size="small" @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button size="small" @click="dataFormSubmitHandle()" type="primary" v-loading="loading">{{ $t('confirm')
      }}</el-button>
    </template>
  </qz-dialog>
</template>

<script>
import request from '@/plugin/axios'

export default {
  name: 'diagnosis-select',
  props: {
    selectedName: {
      type: String,
      default: ''
    },
    selectedCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      title: '选择诊断',
      filterTextDiagnosis: '',
      dataDiagnosis: [],
      defaultProps: {
        children: 'children',
        label: 'diseaseName'
      },
      dataForm: {
        choosedDiagnosis: []
      },
      localSelectedDiagnoses: []
    }
  },
  methods: {
    init(data) {
      this.visible = true
      this.dataForm.choosedDiagnosis = []
      //初始化本地已选择的诊断
      if (this.selectedCode && this.selectedName) {
        const codes = this.selectedCode.split(',')
        const names = this.selectedName.split(',')

        this.localSelectedDiagnoses = codes.map((code, index) => ({
          id: code,
          diagnosisCode: code,
          diseaseName: names[index]
        }))
      } else {
        this.localSelectedDiagnoses = []
      }

      this.$nextTick(() => {
        this.initLeftDiagnosisTreeData()
      })
    },

    initData(data) {
      this.$refs.treeDiagnosis.setCheckedKeys(data.split(','))
    },

    getParams() {
      return this.dataForm.choosedDiagnosis.map(item => item.pkid).join(',')
    },

    validateForm() {
      return this.$refs.dataForm.validate().then(v => v, e => {
      })
    },
    handleCheckChange(checkedNode, isChecked) {
      // 直接根据isChecked状态处理，不做复杂判断
      if (isChecked) {
        // 选中节点
        if (this.hasChildren(checkedNode)) {
          // 有子节点：选择所有叶子节点，不选择自身
          this.selectLeafNodesRecursively(checkedNode);
        } else {
          // 叶子节点：添加到已选择列表
          this.addToSelectedList(checkedNode);
        }
      } else {
        // 取消选中节点
        if (this.hasChildren(checkedNode)) {
          // 有子节点：取消所有叶子节点
          this.unselectLeafNodesRecursively(checkedNode);
        } else {
          // 叶子节点：从已选择列表移除
          this.removeFromSelectedList(checkedNode);
        }
      }
    },

    // 判断节点是否有子节点
    hasChildren(node) {
      return node.children && node.children.length > 0;
    },

    // 递归选择叶子节点
    selectLeafNodesRecursively(parentNode) {
      const traverse = (node) => {
        if (this.hasChildren(node)) {
          // 有子节点：设置树控件显示为选中，但不添加到列表
          this.setTreeNodeChecked(node.id, true);
          // 递归处理子节点
          node.children.forEach(child => traverse(child));
        } else {
          // 叶子节点：添加到已选择列表并设置树控件选中
          this.addToSelectedList(node);
          this.setTreeNodeChecked(node.id, true);
        }
      };

      // 从父节点开始遍历
      traverse(parentNode);
    },

    // 递归取消选择叶子节点
    unselectLeafNodesRecursively(parentNode) {
      const traverse = (node) => {
        if (this.hasChildren(node)) {
          // 有子节点：设置树控件为未选中，递归处理子节点
          this.setTreeNodeChecked(node.id, false);
          node.children.forEach(child => traverse(child));
        } else {
          // 叶子节点：从已选择列表移除并设置树控件未选中
          this.removeFromSelectedList(node);
          this.setTreeNodeChecked(node.id, false);
        }
      };

      // 从父节点开始遍历
      traverse(parentNode);
    },
    initLeftDiagnosisTreeData() {
      if (this.filterTextDiagnosis) {
        request({
          url: 'business/busdiseasetypemanage/listTree',
          method: 'get',
          params: { diseaseName: this.filterTextDiagnosis }
        }).then((res) => {
          this.dataDiagnosis = res
          // 使用id进行精确匹配，避免同名节点问题
          const selectedIds = this.localSelectedDiagnoses.map(s => s.id)
          const checkedIds = []
          const traverse = (nodes) => {
            for (const node of nodes) {
              // 优先使用id匹配，如果id不存在则使用diagnosisCode匹配
              if (selectedIds.includes(node.id) ||
                  this.localSelectedDiagnoses.some(s => s.diagnosisCode === node.diagnosisCode && !selectedIds.includes(s.id))) {
                checkedIds.push(node.id)
              }
              if (node.children && node.children.length > 0) {
                traverse(node.children)
              }
            }
          }
          traverse(this.dataDiagnosis)
          this.$refs.treeDiagnosis.setCheckedKeys(checkedIds)
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      }
    },

    filterNodeDiagnosis(value, data) {
      if (!value) return true
      return data.diseaseName.indexOf(value) !== -1
    },

    handleRemoveTag(index) {
      const totalSelected = this.localSelectedDiagnoses.length
      if (index < totalSelected) {
        const removedDiagnosis = this.localSelectedDiagnoses[index]
        this.localSelectedDiagnoses.splice(index, 1)
        if (this.$refs.treeDiagnosis) {
          // 使用id进行精确匹配，避免同名节点误删
          const node = this.findNodeById(this.dataDiagnosis, removedDiagnosis.id)
          if (node) {
            this.$refs.treeDiagnosis.setChecked(node.id, false)
          }
        }
      } else {
        const newIndex = index - totalSelected
        const removedDiagnosis = this.dataForm.choosedDiagnosis[newIndex]
        this.dataForm.choosedDiagnosis.splice(newIndex, 1)
        if (this.$refs.treeDiagnosis && removedDiagnosis) {
          this.$refs.treeDiagnosis.setChecked(removedDiagnosis.id, false)
        }
      }
    },

    findNodeByDiagnosisCode(nodes, diagnosisCode) {
      for (const node of nodes) {
        if (node.diagnosisCode === diagnosisCode) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeByDiagnosisCode(node.children, diagnosisCode)
          if (found) return found
        }
      }
      return null
    },

    // 新增：通过id精确查找节点
    findNodeById(nodes, nodeId) {
      for (const node of nodes) {
        if (node.id === nodeId) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeById(node.children, nodeId)
          if (found) return found
        }
      }
      return null
    },

    // 添加节点到已选择列表
    addToSelectedList(node) {
      const existingInLocal = this.localSelectedDiagnoses.some(s => s.id === node.id);
      const existingInNew = this.dataForm.choosedDiagnosis.some(s => s.id === node.id);

      if (!existingInLocal && !existingInNew) {
        this.dataForm.choosedDiagnosis.push(node);
      }
    },

    // 从已选择列表中移除节点
    removeFromSelectedList(node) {
      this.dataForm.choosedDiagnosis = this.dataForm.choosedDiagnosis.filter(s => s.id !== node.id);
      this.localSelectedDiagnoses = this.localSelectedDiagnoses.filter(s => s.id !== node.id);
    },

    // 设置树控件节点选中状态
    setTreeNodeChecked(nodeId, checked) {
      if (this.$refs.treeDiagnosis) {
        this.$refs.treeDiagnosis.setChecked(nodeId, checked);
      }
    },

    // 获取节点的所有叶子节点ID
    getAllLeafNodeIds(node) {
      const leafIds = [];
      const traverse = (currentNode) => {
        if (this.hasChildren(currentNode)) {
          currentNode.children.forEach(child => traverse(child));
        } else {
          leafIds.push(currentNode.id);
        }
      };
      traverse(node);
      return leafIds;
    },

    // 获取节点下当前已选中的叶子节点ID
    getSelectedLeafIds(node) {
      const allLeafIds = this.getAllLeafNodeIds(node);
      return allLeafIds.filter(leafId =>
        this.localSelectedDiagnoses.some(s => s.id === leafId) ||
        this.dataForm.choosedDiagnosis.some(s => s.id === leafId)
      );
    },

    // 检查节点是否被选中（基于其叶子节点是否都被选中）
    isNodeSelected(node) {
      if (!this.hasChildren(node)) {
        // 叶子节点：直接检查是否在已选择列表中
        return this.localSelectedDiagnoses.some(s => s.id === node.id) ||
               this.dataForm.choosedDiagnosis.some(s => s.id === node.id);
      } else {
        // 有子节点：检查所有叶子节点是否都被选中
        const leafIds = this.getAllLeafNodeIds(node);
        return leafIds.every(leafId =>
          this.localSelectedDiagnoses.some(s => s.id === leafId) ||
          this.dataForm.choosedDiagnosis.some(s => s.id === leafId)
        );
      }
    },

    async dataFormSubmitHandle() {
      if (this.choosedDiagnosisShow.length === 0) {
        this.$message.error('请选择诊断')
        return
      }
      this.loading = true
      try {
        const allDiagnoses = [
          ...this.localSelectedDiagnoses,
          ...this.dataForm.choosedDiagnosis
        ]
        const finalDiagnosisCode = allDiagnoses.map(item => item.diagnosisCode).join(',')
        const finalDiseaseName = allDiagnoses.map(item => item.diseaseName).join(',')

        this.$emit('callback', {
          diagnosisCode: finalDiagnosisCode,
          diseaseName: finalDiseaseName
        })
        this.visible = false
      } catch (err) {
        console.error(err)
      } finally {
        this.loading = false
      }
    }
  },
  computed: {
    choosedDiagnosisShow() {
      // 如果有已选的诊断，显示已选的和新选的组合，否则显示传入的selectedName
      const existingNames = this.localSelectedDiagnoses.map(v => v.diseaseName)
      const newNames = this.dataForm.choosedDiagnosis.map(v => v.diseaseName)
      return [...existingNames, ...newNames]
    },
    choosedDiagnosisCode() {
      return this.dataForm.choosedDiagnosis.length > 0
        ? (this.selectedCode ? this.selectedCode + ',' : '') + this.dataForm.choosedDiagnosis.map(v => v.diagnosisCode).join(',')
        : this.selectedCode
    }
  }
}
</script>

<style lang="scss" scoped>
.tag-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  min-height: 32px;
  max-height: 120px;
  margin-bottom: 5px;
  overflow-y: auto;
}

.filter-tree-dept {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  overflow-y: auto;
}
</style>