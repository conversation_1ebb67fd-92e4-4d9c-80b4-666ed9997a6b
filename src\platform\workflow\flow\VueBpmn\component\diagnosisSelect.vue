<template>
  <qz-dialog :title="title" :visible.sync="visible" top="6vh" width="600px">
    <div class="KS-Form">
      <el-form ref="dataForm" :model="dataForm" label-width="80px" size="small">
        <el-form-item label="已选诊断" prop="choosedDiagnosis">
          <div class="tag-container">
            <el-tag v-for="(diagnosis, index) in choosedDiagnosisShow" :key="index" closable
              @close="handleRemoveTag(index)" style="margin: 2px 5px 2px 0;">
              {{ diagnosis }}
            </el-tag>
            <div v-if="choosedDiagnosisShow.length === 0" class="el-form-item__error">
              请选择诊断
            </div>
          </div>
        </el-form-item>
        <el-form-item label="输入诊断" prop="diagnosis">
          <el-input placeholder="输入关键字过滤诊断" v-model="filterTextDiagnosis" size="small">
            <i @click="initLeftDiagnosisTreeData" class="el-input__icon el-icon-search" slot="suffix"
              style="cursor: pointer"></i>
          </el-input>
          <el-tree class="filter-tree-dept" :data="dataDiagnosis" show-checkbox node-key="id" :props="defaultProps"
            default-expand-all style="max-height: 300px;margin-top: 5px;" :filter-node-method="filterNodeDiagnosis"
            ref="treeDiagnosis" :expand-on-click-node="false" :highlight-current="true"
            @check-change="handleCheckChange">
          </el-tree>
        </el-form-item>
      </el-form>
    </div>
    <template slot="footer">
      <el-button size="small" @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button size="small" @click="dataFormSubmitHandle()" type="primary" v-loading="loading">{{ $t('confirm')
      }}</el-button>
    </template>
  </qz-dialog>
</template>

<script>
import request from '@/plugin/axios'

export default {
  name: 'diagnosis-select',
  props: {
    selectedName: {
      type: String,
      default: ''
    },
    selectedCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      title: '选择诊断',
      filterTextDiagnosis: '',
      dataDiagnosis: [],
      defaultProps: {
        children: 'children',
        label: 'diseaseName'
      },
      dataForm: {
        choosedDiagnosis: []
      },
      localSelectedDiagnoses: []
    }
  },
  methods: {
    init(data) {
      this.visible = true
      this.dataForm.choosedDiagnosis = []
      //初始化本地已选择的诊断
      if (this.selectedCode && this.selectedName) {
        const codes = this.selectedCode.split(',')
        const names = this.selectedName.split(',')

        this.localSelectedDiagnoses = codes.map((code, index) => ({
          id: code,
          diagnosisCode: code,
          diseaseName: names[index]
        }))
      } else {
        this.localSelectedDiagnoses = []
      }

      this.$nextTick(() => {
        this.initLeftDiagnosisTreeData()
      })
    },

    initData(data) {
      this.$refs.treeDiagnosis.setCheckedKeys(data.split(','))
    },

    getParams() {
      return this.dataForm.choosedDiagnosis.map(item => item.pkid).join(',')
    },

    validateForm() {
      return this.$refs.dataForm.validate().then(v => v, e => {
      })
    },
    handleCheckChange(checkedNode, isChecked) {
      const nodeId = checkedNode.id;

      if (isChecked) {
        // 如果节点有子节点，选择所有子节点
        if (checkedNode.children && checkedNode.children.length > 0) {
          // 选中所有子节点
          this.selectAllChildren(checkedNode.children);
        }

        // 无论是否有子节点，都正常选择当前节点
        const existingInLocal = this.localSelectedDiagnoses.some(s => s.id === nodeId);
        const existingInNew = this.dataForm.choosedDiagnosis.some(s => s.id === nodeId);
        if (!existingInLocal && !existingInNew) {
          this.dataForm.choosedDiagnosis.push(checkedNode);
        }
      } else {
        // 取消选择
        if (checkedNode.children && checkedNode.children.length > 0) {
          // 如果是有子节点的根节点，取消所有子节点
          this.unselectAllChildren(checkedNode.children);
        }

        // 无论是否有子节点，都取消当前节点
        this.dataForm.choosedDiagnosis = this.dataForm.choosedDiagnosis.filter(s => s.id !== nodeId);
        this.localSelectedDiagnoses = this.localSelectedDiagnoses.filter(s => s.id !== nodeId);
      }
    },
    initLeftDiagnosisTreeData() {
      if (this.filterTextDiagnosis) {
        request({
          url: 'business/busdiseasetypemanage/listTree',
          method: 'get',
          params: { diseaseName: this.filterTextDiagnosis }
        }).then((res) => {
          this.dataDiagnosis = res
          // 使用id进行精确匹配，避免同名节点问题
          const selectedIds = this.localSelectedDiagnoses.map(s => s.id)
          const checkedIds = []
          const traverse = (nodes) => {
            for (const node of nodes) {
              // 优先使用id匹配，如果id不存在则使用diagnosisCode匹配
              if (selectedIds.includes(node.id) ||
                  this.localSelectedDiagnoses.some(s => s.diagnosisCode === node.diagnosisCode && !selectedIds.includes(s.id))) {
                checkedIds.push(node.id)
              }
              if (node.children && node.children.length > 0) {
                traverse(node.children)
              }
            }
          }
          traverse(this.dataDiagnosis)
          this.$refs.treeDiagnosis.setCheckedKeys(checkedIds)
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      }
    },

    filterNodeDiagnosis(value, data) {
      if (!value) return true
      return data.diseaseName.indexOf(value) !== -1
    },

    handleRemoveTag(index) {
      const totalSelected = this.localSelectedDiagnoses.length
      if (index < totalSelected) {
        const removedDiagnosis = this.localSelectedDiagnoses[index]
        this.localSelectedDiagnoses.splice(index, 1)
        if (this.$refs.treeDiagnosis) {
          // 使用id进行精确匹配，避免同名节点误删
          const node = this.findNodeById(this.dataDiagnosis, removedDiagnosis.id)
          if (node) {
            this.$refs.treeDiagnosis.setChecked(node.id, false)
          }
        }
      } else {
        const newIndex = index - totalSelected
        const removedDiagnosis = this.dataForm.choosedDiagnosis[newIndex]
        this.dataForm.choosedDiagnosis.splice(newIndex, 1)
        if (this.$refs.treeDiagnosis && removedDiagnosis) {
          this.$refs.treeDiagnosis.setChecked(removedDiagnosis.id, false)
        }
      }
    },

    findNodeByDiagnosisCode(nodes, diagnosisCode) {
      for (const node of nodes) {
        if (node.diagnosisCode === diagnosisCode) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeByDiagnosisCode(node.children, diagnosisCode)
          if (found) return found
        }
      }
      return null
    },

    // 新增：通过id精确查找节点
    findNodeById(nodes, nodeId) {
      for (const node of nodes) {
        if (node.id === nodeId) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeById(node.children, nodeId)
          if (found) return found
        }
      }
      return null
    },

    // 新增：选中所有子节点
    selectAllChildren(children) {
      for (const child of children) {
        // 检查是否已存在
        const existingInLocal = this.localSelectedDiagnoses.some(s => s.id === child.id);
        const existingInNew = this.dataForm.choosedDiagnosis.some(s => s.id === child.id);

        if (!existingInLocal && !existingInNew) {
          // 添加子节点到选择列表
          this.dataForm.choosedDiagnosis.push(child);

          // 如果子节点还有子节点，递归选择其子节点
          if (child.children && child.children.length > 0) {
            this.selectAllChildren(child.children);
          }
        }

        // 在树控件中设置选中状态
        if (this.$refs.treeDiagnosis) {
          this.$refs.treeDiagnosis.setChecked(child.id, true);
        }
      }
    },

    // 新增：取消选中所有子节点
    unselectAllChildren(children) {
      for (const child of children) {
        // 从选择列表中移除
        this.dataForm.choosedDiagnosis = this.dataForm.choosedDiagnosis.filter(s => s.id !== child.id);
        this.localSelectedDiagnoses = this.localSelectedDiagnoses.filter(s => s.id !== child.id);

        // 在树控件中取消选中
        if (this.$refs.treeDiagnosis) {
          this.$refs.treeDiagnosis.setChecked(child.id, false);
        }

        // 递归处理子节点
        if (child.children && child.children.length > 0) {
          this.unselectAllChildren(child.children);
        }
      }
    },

    async dataFormSubmitHandle() {
      if (this.choosedDiagnosisShow.length === 0) {
        this.$message.error('请选择诊断')
        return
      }
      this.loading = true
      try {
        const allDiagnoses = [
          ...this.localSelectedDiagnoses,
          ...this.dataForm.choosedDiagnosis
        ]
        const finalDiagnosisCode = allDiagnoses.map(item => item.diagnosisCode).join(',')
        const finalDiseaseName = allDiagnoses.map(item => item.diseaseName).join(',')

        this.$emit('callback', {
          diagnosisCode: finalDiagnosisCode,
          diseaseName: finalDiseaseName
        })
        this.visible = false
      } catch (err) {
        console.error(err)
      } finally {
        this.loading = false
      }
    }
  },
  computed: {
    choosedDiagnosisShow() {
      // 如果有已选的诊断，显示已选的和新选的组合，否则显示传入的selectedName
      const existingNames = this.localSelectedDiagnoses.map(v => v.diseaseName)
      const newNames = this.dataForm.choosedDiagnosis.map(v => v.diseaseName)
      return [...existingNames, ...newNames]
    },
    choosedDiagnosisCode() {
      return this.dataForm.choosedDiagnosis.length > 0
        ? (this.selectedCode ? this.selectedCode + ',' : '') + this.dataForm.choosedDiagnosis.map(v => v.diagnosisCode).join(',')
        : this.selectedCode
    }
  }
}
</script>

<style lang="scss" scoped>
.tag-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  min-height: 32px;
  max-height: 120px;
  margin-bottom: 5px;
  overflow-y: auto;
}

.filter-tree-dept {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  overflow-y: auto;
}
</style>