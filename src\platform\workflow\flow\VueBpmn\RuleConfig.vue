<template>
  <el-dialog :visible.sync="dialog" :close-on-click-modal="false" title="条件配置" append-to-body width="90%">
    <div class="condition-container" @click.self="closeAllPopovers">
      <el-form v-for="(group, groupIndex) in conditionGroups" :key="groupIndex" class="group-container">
        <el-card shadow="hover">
          <div class="group-header">
            <div class="group-title">
              条件组 {{ groupIndex + 1 }}
              <el-select v-model="group.withinLogic" size="mini" style="width: 60px; margin-left: 10px;">
                <el-option label="且" value="&&" />
                <el-option label="或" value="||" />
              </el-select>
            </div>
            <div>
              <el-button v-if="conditionGroups.length > 1" type="text" icon="el-icon-close"
                @click="removeConditionGroup(groupIndex)"></el-button>
            </div>
          </div>
          <div v-for="(condition, condIndex) in group.conditions" :key="condIndex" class="condition-item">
            <el-row :gutter="10">
              <el-col :span="3">
                <el-select v-model="condition.table" placeholder="条件"
                  @change="updateConditionFields(condition, groupIndex, condIndex)">
                  <el-option-group v-for="group in options" :key="group.label" :label="group.label">
                    <el-option v-for="item in group.tableLists" :key="item.value" :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-option-group>
                </el-select>
              </el-col>
              <el-col :span="3" v-if="shouldShowField2(condition)">
                <template v-if="condition.table === 'bus_medical_records_inspection_items'">
                  <el-popover :key="condition.fieldNameDisplay + '_' + condition.field" placement="bottom" width="300"
                    trigger="manual" v-model="condition.treePopoverVisible"
                    :ref="'popover_' + groupIndex + '_' + condIndex">
                    <el-tree :data="inspectionTreeOptionsMap[groupIndex + '_' + condIndex] || []"
                      :props="{ label: 'inspectionName', children: 'children' }" node-key="id" highlight-current
                      :expand-on-click-node="false"
                      @node-click="data => handleFieldTreeSelect(data, condition, groupIndex, condIndex)"
                      :current-node-key="condition.field" style="max-height:300px;overflow:auto;"
                      expand-on-click-node=true>
                      <span slot-scope="{ node, data }">{{ data.inspectionName }}</span>
                    </el-tree>
                    <el-input slot="reference" v-model="inspectionTreeInputMap[groupIndex + '_' + condIndex]"
                      placeholder="请输入关键字" style="width: 100%" @focus="condition.treePopoverVisible = true"
                      @input="onInspectionTreeInput(groupIndex, condIndex)"
                      @keyup.enter.native="onInspectionTreeSearch(groupIndex, condIndex)">
                      <template slot="suffix">
                        <span style="
                                    display: flex;
                                    align-items: center;
                                    position: absolute;
                                    right: 8px;
                                    height: 100%;
                                    top: 0;
                                    line-height: normal;
                                  ">
                          <i class="el-icon-search" style="cursor:pointer;margin-right:6px;"
                            @click.stop="onInspectionTreeSearch(groupIndex, condIndex)"></i>
                          <i class="el-icon-circle-close" style="cursor:pointer;"
                            @click.stop="clearFieldTree(condition, groupIndex, condIndex)"></i>
                        </span>
                      </template>
                    </el-input>
                  </el-popover>
                </template>
                <el-select v-else v-model="condition.field" placeholder="条件字段">
                  <el-option v-for="item in fieldLists.filter(field => field.parent === condition.table)"
                    :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="2" v-if="shouldShowField3(condition)">
                <el-select v-model="condition.medicationTime" placeholder="用药时间">
                  <el-option v-for="item in medicationTimeList" :key="item.value" :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="3" v-if="shouldShowField(condition)">
                <!--<el-select v-model="condition.operator" placeholder="运算符">-->
                <!--<el-option v-for="item in operatorOptions" :key="item.value" :label="item.label" :value="item.value">-->
                <!--</el-option>-->
                <!--</el-select>-->
                <el-select v-model="condition.operator" placeholder="运算符">
                  <el-option v-for="item in dynamicOperatorOptions(condition)" :key="item.value" :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="12" v-if="shouldShowField(condition)">
                <div>
                  <div v-if="condition.table === 'bus_medical_records_orders' && condition.field === 'using_drugs'">
                    <el-select v-model="condition.value" filterable style="width: 100%" remote reserve-keyword
                      placeholder="请选择药品名称" :remote-method="remoteMethod" :loading="loading">
                      <el-option v-for="item in drugNameOptions" :key="item.value" :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
                  <div
                    v-else-if="condition.table === 'bus_medical_records_orders' && condition.field === 'recipe_generic_drugs'">
                    <!--   multiple-->
                    <el-select v-model="condition.value" filterable style="width: 100%" remote reserve-keyword
                      placeholder="请选择药品通用名称" :remote-method="remoteRecipeGenericDrugsMethod" :loading="loading">
                      <el-option v-for="item in recipeGenericNameOptions" :key="item.value" :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
                  <div v-else-if="condition.table === 'bus_medical_records_diagnosis' &&
                    (condition.field === 'main_diagnosis_status' || condition.field === 'other_diagnosis_status')
                    && (condition.operator === '==' || condition.operator === '!=')">
                    <el-select v-model="condition.value"  reserve-keyword style="width: 100%" :value="getDiagnosisDisplayValue(condition)"
                      :loading="!showDiagnosisTree" placeholder="请选择诊断" @focus="handleDiagnosisClick(condition)"
                      @blur="showDiagnosisTree = false">
                    </el-select>
                  </div>
                  <div v-else-if="condition.table === 'bus_medical_records_surgical' && condition.field === 'operation_name'
                    && (condition.operator === '==' || condition.operator === '!=')">
                    <el-select v-model="condition.value" reserve-keyword style="width: 100%" :value="getSurgeryDisplayValue(condition)"
                      placeholder="请选择手术名称" @focus="handleSurgeryClick(condition)">
                    </el-select>
                  </div>
                  <div v-else>
                    <el-form-item :prop="condition.value"
                      :rules="[{ required: true, message: '此项不能为空', trigger: 'blur' }]">
                      <el-input v-model="condition.value" placeholder="值"></el-input></el-form-item>
                  </div>
                </div>
              </el-col>
              <el-col :span="3" v-if="!shouldShowField(condition)">
                <el-select v-model="condition.value" placeholder="请选择">
                  <!-- 其他运算符... -->
                  <el-option v-for="item in whetherOrNot" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="1">
                <el-button type="text" icon="el-icon-remove" @click="removeCondition(groupIndex, condIndex)"
                  :disabled="group.conditions.length === 1"></el-button>
              </el-col>
            </el-row>
            <div class="logic-operator" v-if="condIndex < group.conditions.length - 1">
              {{ group.withinLogic === '&&' ? '且' : '或' }}
            </div>
          </div>

          <el-button type="text" icon="el-icon-circle-plus" @click="addCondition(groupIndex)">添加条件
          </el-button>
        </el-card>

        <div class="group-operator" v-if="groupIndex < conditionGroups.length - 1">
          <el-select v-model="group.betweenLogic" size="mini" style="width:60px;">
            <el-option label="且" value="AND" />
            <el-option label="或" value="OR" />
          </el-select>
        </div>
      </el-form>

      <el-button type="text" icon="el-icon-circle-plus" @click="addConditionGroup">添加条件组
      </el-button>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" size="small" @click="dialog = false">取消</el-button>
      <el-button type="primary" size="small" @click="doSubmit">确认</el-button>
    </div>
    <surgery-select ref="surgerySelect"
      :selectedName="currentCondition ? displayValues[`surgery_${currentCondition.value}`] : ''"
      :selectedCode="currentCondition ? currentCondition.value : ''" @callback="handleSurgerySelectCallback"
      v-if="surgerySelectDialog" />
    <diagnosis-select ref="diagnosisSelect" @callback="handleDiagnosisSelectCallback" v-if="diagnosisSelectDialog"
      :selectedName="currentCondition ? displayValues[`diagnosis_${currentCondition.value}`] : ''"
      :selectedCode="currentCondition ? currentCondition.value : ''" />
  </el-dialog>
</template>

<script>

import request from '@/plugin/axios'
import surgerySelect from './component/surgerySelect.vue'
import diagnosisSelect from './component/diagnosisSelect.vue'
import * as api from '../api'
export default {
  components: { surgerySelect, diagnosisSelect },
  data() {
    return {
      surgerySelectDialog: false,
      diagnosisSelectDialog: false,
      selectedValue: '',
      showTree: false,
      treeProps: {
        children: 'children',
        label: 'surgeryName'
      },
      drugNameOptions: [],
      recipeGenericNameOptions: [],
      loading: false,
      surgeryOptions: [],
      loading2: false,
      loading3: false,
      showDiagnosisTree: false,
      diagnosisTreeProps: {
        children: 'children',
        label: 'diseaseName'
      },
      diagnosisOptions: [],
      dialog: false,
      conditionExpression: {},
      currentCondition: null,
      displayValues: {},
      options: [{
        label: '常用条件',
        tableLists: [
          { value: '_cephalosporinsAllergy_', label: '是否有头孢类过敏史' },
          { value: '_penicillinAllergy_', label: '是否有青霉素类过敏史' },
          { value: '_Ccr_', label: 'Ccr肌酐清除率判断' },
          { value: '_bmi_', label: 'BMI值判断' },
          { value: '_anticoagulantOrAntiplateletTherapy_', label: '是否口服抗凝或抗血小板药物' },
          { value: '_surgicalDuration_', label: '手术时长判断(小时)' },
          { value: '_dangerousFactors_', label: '兼具公共危险因素' }
        ]
      }, {
        label: '自定义条件',
        tableLists: [
          { value: 'bus_medical_records_basic', label: '基本信息表' },
          { value: 'bus_medical_records_orders', label: '医嘱表' },
          { value: 'bus_medical_records_surgical', label: '手术表' },
          { value: 'bus_medical_records_diagnosis', label: '诊断表' },
          { value: 'bus_medical_records_inspection_items', label: '检查明细表' }
        ]
      }],
      fieldLists: [
        { value: 'age', label: '年龄  ', parent: 'bus_medical_records_basic' },
        { value: 'height', label: '身高', parent: 'bus_medical_records_basic' },
        { value: 'weight', label: '体重', parent: 'bus_medical_records_basic' },
        { value: 'gender', label: '性别', parent: 'bus_medical_records_basic' },
        { value: 'using_drugs', label: '药品名称', parent: 'bus_medical_records_orders' },
        { value: 'recipe_generic_drugs', label: '药品通用名', parent: 'bus_medical_records_orders' },
        { value: 'other_order', label: '其他医嘱', parent: 'bus_medical_records_orders' },
        { value: 'operation_name', label: '手术名称', parent: 'bus_medical_records_surgical' },
        { value: 'incision_healing_level', label: '切口愈合等级', parent: 'bus_medical_records_surgical' },
        { value: 'leukocyte_amount', label: '白细胞计数', parent: 'bus_medical_records_inspection_items' },
        { value: 'procalcitonin_amount', label: '降钙素原', parent: 'bus_medical_records_inspection_items' },
        { value: 'main_diagnosis_status', label: '主诊断', parent: 'bus_medical_records_diagnosis' },
        { value: 'other_diagnosis_status', label: '其他诊断', parent: 'bus_medical_records_diagnosis' }
      ],
      medicationTimeList: [
        { value: '0', label: '全部' },
        { value: '1', label: '手术前' },
        { value: '2', label: '手术中' },
        { value: '3', label: '手术后' }
      ],
      operatorOptions: [
        { value: '>', label: '大于' },
        { value: '<', label: '小于' },
        { value: '>=', label: '大于等于' },
        { value: '<=', label: '小于等于' },
        { value: '==', label: '等于' },
        { value: '!=', label: '不等于' },
        { value: 'include', label: '包含关键字' }
      ],
      whetherOrNot: [
        { value: '_true_', label: '是' },
        { value: '_false_', label: '否' }
      ],
      conditionGroups: [
        {
          conditions: [this.createEmptyCondition()],
          withinLogic: '&&',
          betweenLogic: 'OR'
        }
      ],
      inspectionTreeOptions: [],
      inspectionTreeLoaded: false,
      inspectionTreeLoading: false,
      inspectionTreeSearchTextMap: {},
      inspectionTreeInputMap: {},
      inspectionTreeSelectedMap: {},
      inspectionTreeOptionsMap: {},
      inspectionTreeLoadingMap: {},
    }
  },
  watch: {
    dialog(val) {
      if (val) {
        this.initDisplayValues();
      } else {
        this.closeAllPopovers();
      }
    }
  },
  computed: {
    shouldShowField() {
      return (condition) => {
        return ['_infected_', '_penicillinAllergy_', '_cephalosporinsAllergy_', '_dangerousFactors_','_anticoagulantOrAntiplateletTherapy_'].findIndex(x => x === condition.table) === -1
      }
    },
    dynamicOperatorOptions() {
      return (condition) => {
        if (condition.table === '_Ccr_' || condition.table === '_bmi_' || condition.table === '_surgicalDuration_') {
          return [{ value: '>', label: '大于' },
            { value: '<', label: '小于' },
            { value: '>=', label: '大于等于' },
            { value: '<=', label: '小于等于' }]
        }
        if (!condition.table || !condition.field) return [{ value: '==', label: '等于' }]
        // 药品名称只显示等于/不等于
        if (condition.table === 'bus_medical_records_basic') {
          if (condition.field === 'age' || condition.field === 'height' || condition.field === 'weight') {
            return [{ value: '>', label: '大于' },
            { value: '<', label: '小于' },
            { value: '>=', label: '大于等于' },
            { value: '<=', label: '小于等于' }]
          } else if (condition.field === 'gender') {
            return [{ value: '==', label: '等于' }, { value: '!=', label: '不等于' }]
          }
          if (condition.table === 'bus_medical_records_orders') {
            if (condition.field === 'using_drugs' || condition.field === 'recipe_generic_drugs') {
              return [{ value: '==', label: '等于' }, { value: '!=', label: '不等于' }]
            } else if (condition.field === 'other_order') {
              return [
                { value: '==', label: '等于' },
                { value: '!=', label: '不等于' },
                { value: 'include', label: '包含关键字' }
              ]
            }
          }
        }
        if (condition.table === 'bus_medical_records_surgical') {
          return [
            { value: '==', label: '等于' },
            { value: '!=', label: '不等于' },
            { value: 'include', label: '包含关键字' }
          ]
        }
        if (condition.table === 'bus_medical_records_diagnosis') {
          return [{ value: '==', label: '等于' },
          { value: '!=', label: '不等于' },
          { value: 'include', label: '包含关键字' }]
        }
        if (condition.table === 'bus_medical_records_inspection_items') {
          return [{ value: '>', label: '大于' },
          { value: '<', label: '小于' },
          { value: '>=', label: '大于等于' },
          { value: '<=', label: '小于等于' }]
        }
        // 默认情况
        return [
          { value: '==', label: '等于' },
          { value: '!=', label: '不等于' },
          { value: 'include', label: '包含关键字' }
        ]
      }
    },
    shouldShowField2() {
      return (condition) => {
        let flag = false
        for (const item of this.options[0].tableLists) {
          if (item.value === condition.table) {
            flag = true
            break
          }
        }
        return !flag
      }
    },
    shouldShowField3() {
      return (condition, field) => {
        if (condition.table === 'bus_medical_records_inspection_items' || condition.table === 'bus_medical_records_orders') {
          return true
        }
      }
    },
    getDiagnosisDisplayValue() {
      return (condition) => {
        if (condition.table === 'bus_medical_records_diagnosis' &&
          (condition.field === 'main_diagnosis_status' || condition.field === 'other_diagnosis_status') &&
          condition.value) {
          if (!this.displayValues[`diagnosis_${condition.value}`]) {
            api.getDName(condition.value).then(name => {
              if (name) {
                this.$set(this.displayValues, `diagnosis_${condition.value}`, name)
              }
            })
          }
          return this.displayValues[`diagnosis_${condition.value}`] || condition.value || ''
        }
        return ''
      }
    },
    getSurgeryDisplayValue() {
      return (condition) => {
        if (condition.table === 'bus_medical_records_surgical' &&
          condition.field === 'operation_name' &&
          condition.value) {
          if (!this.displayValues[`surgery_${condition.value}`]) {
            api.getSurgeryName(condition.value).then(name => {
              if (name) {
                this.$set(this.displayValues, `surgery_${condition.value}`, name)
              }
            })
          }
          return this.displayValues[`surgery_${condition.value}`] || condition.value || ''
        }
        return ''
      }
    }
  },
  methods: {
    closeAllPopovers() {
      this.conditionGroups.forEach(group => {
        group.conditions.forEach(cond => {
          cond.treePopoverVisible = false
        })
      })
    },
    async fetchDiagnosisName(code) {
      if (!code) return ''
      try {
        const name = await api.getDName(code)
        this.$set(this.displayValues, `diagnosis_${code}`, name)
        return name
      } catch (error) {
        console.error('Failed to get diagnosis name:', error)
        return code
      }
    },
    async fetchSurgeryName(code) {
      if (!code) return ''
      try {
        const name = await api.getSurgeryName(code)
        this.$set(this.displayValues, `surgery_${code}`, name)
        return name
      } catch (error) {
        console.error('Failed to get surgery name:', error)
        return code
      }
    },
    handleSurgerySelectCallback(data) {
      if (this.currentCondition) {
        this.currentCondition.value = data.operationCode
        this.$set(this.displayValues, `surgery_${data.operationCode}`, data.surgeryName)
      }
      this.surgerySelectDialog = false
      this.currentCondition = null
    },
    handleNodeClick2(data) {
      this.surgerySelectDialog = true
      this.$nextTick(() => {
        this.$refs.surgerySelect.init(data)
      })
    },
    handleNodeClick(data) {
      this.selectedValue = data.label
      this.showTree = false
    },
    async remoteMethod(query) {
      if (!query) {
        return
      }
      this.loading = true
      request({
        url: '/busDrugManage/list',
        method: 'get',
        params: { drugName: query }
      }).then(res => {
        this.loading = false
        this.drugNameOptions = res.map(item => {
          return {
            value: item.drugName,
            label: item.drugName
          }
        })
      }).catch(() => {
        this.loading = false
      })
    },
    async remoteRecipeGenericDrugsMethod(query) {
      if (!query) {
        return
      }
      this.loading = true
      request({
        url: '/busDrugManage/getRecipeGenericName',
        method: 'get',
        params: { recipeGenericName: query }
      }).then(res => {
        this.loading = false
        this.recipeGenericNameOptions = res.map(item => {
          return {
            value: item,
            label: item
          }
        })
      }).catch(() => {
        this.loading = false
      })
    },
    async remoteMethodSurgery(query) {
      if (!query) {
        return
      }
      this.loading2 = true
      request({
        url: '/surgery-management/list',
        method: 'get',
        params: { surgeryName: query }
      }).then(res => {
        this.loading2 = false
        this.surgeryOptions = res.map(item => {
          return {
            value: item.surgeryName,
            label: item.surgeryName
          }
        })
      }).catch(() => {
        this.loading2 = false
      })
    },
    createEmptyCondition() {
      return {
        table: '',
        field: '',
        operator: '==',
        medicationTime: '0',
        value: '',
        fieldCascaderValue: [],
        fieldSelectLoading: false,
        fieldNameDisplay: '',
        treePopoverVisible: false
      }
    },
    addCondition(groupIndex) {
      this.conditionGroups[groupIndex].conditions.push(this.createEmptyCondition())
    },
    removeCondition(groupIndex, condIndex) {
      this.conditionGroups[groupIndex].conditions.splice(condIndex, 1)
    },
    addConditionGroup() {
      this.conditionGroups.push({
        conditions: [this.createEmptyCondition()],
        withinLogic: '&&',
        betweenLogic: 'OR'
      })
    },
    parseExpression(conditionExpression) {
      try {
        if (conditionExpression) {
          this.conditionGroups = JSON.parse(conditionExpression)
          console.log('this.conditionGroups',this.conditionGroups)
          this.$nextTick(() => {
            this.initDisplayValues()
          })
        } else {
          this.conditionGroups = [
            {
              conditions: [this.createEmptyCondition()],
              withinLogic: '&&',
              betweenLogic: 'OR'
            }
          ]
        }
      } catch (e) {
        this.error = e.message
      }
    },
    parseConditionGroups(expression) {
      const exprMatch = expression.match(/^\${(.*?)}$/)
      if (!exprMatch) throw new Error('Invalid EL expression')
      expression = exprMatch[1]
      const remainingExpr = expression.trim()

      const groups = []
      const groupsList = remainingExpr.split(/(AND|OR)/).map(s => s.trim())
      console.log('groupsList ', groupsList)
      let prevMatch
      for (const g of groupsList) {
        if (g === 'AND' || g === 'OR') {
          prevMatch = g
          continue
        }
        const exprMatch = g.match(/^\((.*)\)/)
        if (!exprMatch) throw new Error('Invalid EL expression')
        const groupContent = exprMatch[1]
        const logicOperator = prevMatch || 'AND' // 默认组间逻辑

        const conditions = this.parseGroupConditions(groupContent)

        const group = {
          conditions: conditions,
          withinLogic: this.detectWithinLogic(groupContent),
          betweenLogic: logicOperator
        }
        if (prevMatch) {
          groups[groups.length - 1].betweenLogic = prevMatch || 'AND'
        }
        groups.push(group)
        if (groups.length > 0) {
          groups[groups.length - 1].betweenLogic = 'AND' // 默认值
        }
      }

      return groups
    },
    parseGroupConditions(groupStr) {
      const conditions = []
      const tokens = groupStr.split(/(?:&&|\|\|)(?=\s*|$)/g).map(s => s.trim());

      for (const token of tokens) {
        if (token === '&&' || token === '||') {
          continue
        }
        if (token.includes('contains')) {
          const parts = token.match(/(!*)([a-zA-Z_-]+)\.(contains)\(['"]([^'"]+)['"]\)/)
          if (parts) {
            const split = parts[2].split('____')
            conditions.push({
              table: split[0],
              field: split[1],
              operator: parts[1] ? '!=' : '==',
              value: parts[4]
            })
          }
        } else {
          const parts = token.match(/(\S+)\s+([!=<>]+)\s+(.+)/)
          if (parts) {
            const split = parts[1].split('____')
            conditions.push({
              table: split[0],
              field: split[1],
              operator: parts[2],
              value: this.parseValue(parts[3])
            })
          }
        }
      }
      return conditions.length > 0 ? conditions : [this.createEmptyCondition()]
    },
    detectWithinLogic(groupStr) {
      return groupStr.includes(' || ') ? '||' : '&&'
    },
    parseValue(val) {
      if (/^['"](.*)['"]$/.test(val)) {
        return val.slice(1, -1)
      }
      if (val.toLowerCase() === 'true') return true
      if (val.toLowerCase() === 'false') return false
      if (!isNaN(val)) return Number(val)
      return val
    },
    doSubmit() {
      const cleanGroups = this.conditionGroups.map(group => ({
        ...group,
        conditions: group.conditions.map(cond => ({
          table: cond.table,
          field: cond.field,
          operator: cond.operator,
          medicationTime: cond.medicationTime,
          value: cond.value
        }))
      }))
      this.$emit('callback', JSON.stringify(cleanGroups))
      this.dialog = false
    },
    removeConditionGroup(groupIndex) {
      this.conditionGroups.splice(groupIndex, 1)
    },
    async updateConditionFields(condition, groupIndex, condIndex) {
      condition.field = ''
      condition.operator = ''
      condition.value = ''
      condition.fieldNameDisplay = ''
      if (typeof groupIndex !== 'undefined' && typeof condIndex !== 'undefined') {
        const key = groupIndex + '_' + condIndex;
        this.inspectionTreeSearchTextMap[key] = '';
        this.inspectionTreeOptionsMap[key] = [];
      }
    },

    handleDiagnosisClick(condition) {

      this.diagnosisSelectDialog = true
      this.currentCondition = condition
      this.$nextTick(() => {
        this.$refs.diagnosisSelect.init()
      })
    },
    handleDiagnosisNodeClick(data) {
      console.log('handleDiagnosisNodeClick', data)
      if (this.currentCondition) {
        this.currentCondition.value = data.diagnosisCode
      }
      this.showDiagnosisTree = false
    },
    handleDiagnosisSelectCallback(data) {
      console.log('handleDiagnosisSelectCallback', data)
      if (this.currentCondition) {
        this.currentCondition.value = data.diagnosisCode
        this.$set(this.displayValues, `diagnosis_${data.diagnosisCode}`, data.diseaseName)
      }
      this.diagnosisSelectDialog = false
      this.currentCondition = null
    },
    handleSurgeryClick(condition) {
      this.surgerySelectDialog = true
      this.currentCondition = condition
      this.$nextTick(() => {
        this.$refs.surgerySelect.init()
      })
    },
    handleSurgeryNodeClick(data) {
      if (this.currentCondition) {
        this.currentCondition.value = data.surgeryCode
      }
      this.showTree = false
    },
    async initDisplayValues() {
      for (let groupIndex = 0; groupIndex < this.conditionGroups.length; groupIndex++) {
        const group = this.conditionGroups[groupIndex];
        for (let condIndex = 0; condIndex < group.conditions.length; condIndex++) {
          const condition = group.conditions[condIndex];
          if (condition.table === 'bus_medical_records_inspection_items') {
            const key = groupIndex + '_' + condIndex;
            if (condition.field) {
              if (!this.inspectionTreeInputMap[key]) {
                await this.onInspectionTreeSearch(groupIndex, condIndex);
                const findNode = (tree, id) => {
                  for (const node of tree) {
                    if (String(node.id) === String(id)) return node
                    if (node.children) {
                      const found = findNode(node.children, id)
                      if (found) return found
                    }
                  }
                  return null
                }
                const node = findNode(this.inspectionTreeOptionsMap[key] || [], condition.field)
                condition.fieldNameDisplay = node ? node.inspectionName : ''
                this.inspectionTreeInputMap[key] = condition.fieldNameDisplay;
                this.inspectionTreeSelectedMap[key] = node;
              }
            } else {
              condition.fieldNameDisplay = '';
              this.inspectionTreeInputMap[key] = '';
              this.inspectionTreeSelectedMap[key] = null;
              this.inspectionTreeOptionsMap[key] = [];
            }
          }
        }
      }
      this.$forceUpdate()
    },
    onInspectionTreeInput(groupIndex, condIndex) {
      const key = groupIndex + '_' + condIndex;
      this.inspectionTreeSelectedMap[key] = null;
      this.$set(this.inspectionTreeOptionsMap, key, []);
    },
    async onInspectionTreeSearch(groupIndex, condIndex) {
      const key = groupIndex + '_' + condIndex;
      const name = this.inspectionTreeInputMap[key] || '';
      if (!name) {
        this.$set(this.inspectionTreeOptionsMap, key, []);
        this.conditionGroups[groupIndex].conditions[condIndex].treePopoverVisible = false;
        return;
      }
      this.$set(this.inspectionTreeLoadingMap, key, true);
      try {
        const res = await request({
          url: '/inspection-info/queryName',
          method: 'get',
          params: { name }
        });
        const arr = Array.isArray(res) ? res : [];
        this.$set(this.inspectionTreeOptionsMap, key, arr);
        if (arr.length > 0) {
          this.conditionGroups[groupIndex].conditions[condIndex].treePopoverVisible = false;
          this.$nextTick(() => {
            this.conditionGroups[groupIndex].conditions[condIndex].treePopoverVisible = true;
          });
        } else {
          this.conditionGroups[groupIndex].conditions[condIndex].treePopoverVisible = false;
        }
      } catch (e) {
        this.$set(this.inspectionTreeOptionsMap, key, []);
        this.conditionGroups[groupIndex].conditions[condIndex].treePopoverVisible = false;
        this.$message.error('加载字段数据失败');
      } finally {
        this.$set(this.inspectionTreeLoadingMap, key, false);
      }
    },
    handleFieldTreeSelect(data, condition, groupIndex, condIndex) {

      if (data.children && data.children.length > 0) {
        return;
      }
      const key = groupIndex + '_' + condIndex;
      condition.field = data.id;
      condition.fieldNameDisplay = data.inspectionName;
      this.inspectionTreeInputMap[key] = data.inspectionName;
      this.inspectionTreeSelectedMap[key] = data;
      condition.treePopoverVisible = false;
      this.$nextTick(() => {
        if (document.activeElement) document.activeElement.blur();
      });
    },
    clearFieldTree(condition, groupIndex, condIndex) {
      const key = groupIndex + '_' + condIndex;
      condition.field = '';
      condition.fieldNameDisplay = '';
      this.inspectionTreeInputMap[key] = '';
      this.inspectionTreeSelectedMap[key] = null;
      this.inspectionTreeOptionsMap[key] = [];
      condition.treePopoverVisible = false;
    },
    handleFieldCascaderChange(val, condition) {
      if (!Array.isArray(val)) val = val ? [val] : []
      condition.field = val.length ? val[val.length - 1] : ''
      condition.fieldCascaderValue = val
    }
  },

}

</script>

<style scoped>
.condition-container {
  padding: 20px;
}

.group-container {
  margin-bottom: 20px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-weight: bold;
}

.group-title {
  display: flex;
  align-items: center;
}

.condition-item {
  margin-bottom: 15px;
  position: relative;
}

.logic-operator {
  text-align: center;
  color: #666;
  padding: 2px 0;
}

.group-operator {
  text-align: center;
  color: #666;
  padding: 15px 0;
  font-weight: bold;
}

.el-card {
  margin-bottom: 10px;
}

/* 让el-input右侧自定义图标始终垂直居中 */
:deep(.el-input__suffix) {
  display: flex !important;
  align-items: center;
  height: 100%;
}

:deep(.el-input__suffix .el-icon-search),
:deep(.el-input__suffix .el-icon-circle-close) {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  font-size: 18px;
  line-height: 1;
}
</style>
