<template>
  <qz-dialog :title="title" :visible.sync="visible" top="6vh" width="600px" @close="visible = false"
    v-loading="loading">
    <div class="KS-Form">
      <el-form ref="dataForm" :model="dataForm" label-width="80px" size="small">
        <el-form-item label="已选手术" :class="{ 'is-error': choosedSurgeryShow.length === 0 }">
          <div class="tag-container">
            <el-tag v-for="(surgery, index) in choosedSurgeryShow" :key="index" closable @close="handleRemoveTag(index)"
              style="margin: 2px 5px 2px 0;">
              {{ surgery }}
            </el-tag>
            <div v-if="choosedSurgeryShow.length === 0" class="el-form-item__error">
              请选择手术
            </div>
          </div>
        </el-form-item>
        <el-form-item label="输入手术">
          <el-input placeholder="输入关键字过滤手术" v-model="filterTextSurgery" size="small" clearable
            @clear="dataSurgery = []">
            <i @click="initLeftSurgeryTreeData" class="el-input__icon el-icon-search" slot="suffix"
              style="cursor: pointer"></i>
          </el-input>
          <el-tree class="filter-tree-dept" :data="dataSurgery" show-checkbox node-key="id" :props="defaultProps"
            style="max-height: 300px;margin-top: 5px;" :filter-node-method="filterNodeSurgery" ref="treeSurgery"
            :expand-on-click-node="false" :highlight-current="true" @check-change="handleCheckChange">
          </el-tree>
        </el-form-item>
      </el-form>
    </div>
    <template slot="footer">
      <el-button size="small" @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button size="small" @click="dataFormSubmitHandle()" type="primary" v-loading="loading">{{ $t('confirm')
      }}</el-button>
    </template>
  </qz-dialog>
</template>

<script>
/**
 * @name surgery-select (组件名称)
 * @module 组件存放位置
 * @desc 组件描述
 * <AUTHOR>
 * @param {Object} [title]    - 参数说明
 * @param {String} [columns] - 参数说明
 * @example 调用示例
 *  <KS-Form></KS-Form>
 */
import * as api from '../../api'

export default {
  name: 'surgery-select',
  components: {},
  props: {
    selectedName: {
      type: String,
      default: ''
    },
    selectedCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      title: '选择手术',
      filterTextSurgery: '',
      dataSurgery: [],
      defaultProps: {
        children: 'children',
        label: 'surgeryName'
      },
      dataForm: {
        choosedSurgery: []
      },
      localSelectedSurgeries: [] // 用于本地存储已选择的手术
    }
  },
  mounted() {
  },
  methods: {
    init(data) {
      this.visible = true
      this.dataForm.choosedSurgery = []

      // 初始化本地已选择的手术
      if (this.selectedCode && this.selectedName) {
        const codes = this.selectedCode.split(',')
        const names = this.selectedName.split(',')

        this.localSelectedSurgeries = codes.map((code, index) => ({
          id: code,
          operationCode: code,
          surgeryName: names[index]
        }))
      } else {
        this.localSelectedSurgeries = []
      }

      this.$nextTick(() => {
        this.initLeftSurgeryTreeData()
      })
    },
    initData(data) {
      this.$refs.treeSurgery.setCheckedKeys(data.split(','))
    },
    getParams() {
      return this.dataForm.choosedSurgery.map(item => item.pkid).join(',')
    },
    validateForm() {
      return this.$refs.dataForm.validate().then(v => v, e => {
      })
    },
    handleCheckChange(checkedNode, isChecked) {
      if (isChecked) {
        this.handleNodeSelect(checkedNode);
      } else {
        this.handleNodeUnselect(checkedNode);
      }
    },

    // 处理节点选择
    handleNodeSelect(node) {
      if (this.hasChildren(node)) {
        // 有子节点：递归选择所有叶子节点，不选择自身
        this.selectLeafNodesRecursively(node);
      } else {
        // 叶子节点：直接添加到已选择列表
        this.addToSelectedList(node);
      }
    },

    // 处理节点取消选择
    handleNodeUnselect(node) {
      if (this.hasChildren(node)) {
        // 有子节点：取消该节点下所有已选择的叶子节点
        this.unselectLeafNodesRecursively(node);
      } else {
        // 叶子节点：从已选择列表中移除
        this.removeFromSelectedList(node);
      }
    },

    // 判断节点是否有子节点
    hasChildren(node) {
      return node.children && node.children.length > 0;
    },

    // 递归选择叶子节点
    selectLeafNodesRecursively(parentNode) {
      const traverse = (node) => {
        if (this.hasChildren(node)) {
          // 有子节点：设置树控件显示为选中，但不添加到列表
          this.setTreeNodeChecked(node.id, true);
          // 递归处理子节点
          node.children.forEach(child => traverse(child));
        } else {
          // 叶子节点：添加到已选择列表并设置树控件选中
          this.addToSelectedList(node);
          this.setTreeNodeChecked(node.id, true);
        }
      };

      // 从父节点开始遍历
      traverse(parentNode);
    },

    // 递归取消选择叶子节点
    unselectLeafNodesRecursively(parentNode) {
      const traverse = (node) => {
        if (this.hasChildren(node)) {
          // 有子节点：设置树控件为未选中，递归处理子节点
          this.setTreeNodeChecked(node.id, false);
          node.children.forEach(child => traverse(child));
        } else {
          // 叶子节点：从已选择列表移除并设置树控件未选中
          this.removeFromSelectedList(node);
          this.setTreeNodeChecked(node.id, false);
        }
      };

      // 从父节点开始遍历
      traverse(parentNode);
    },
    initLeftSurgeryTreeData() {
      if (this.filterTextSurgery) {
        this.loading = true
        api.surgeryList(this.filterTextSurgery).then((res) => {
          this.dataSurgery = res
          // 使用id进行精确匹配，避免同名节点问题
          const selectedIds = this.localSelectedSurgeries.map(s => s.id)
          const checkedIds = []
          const traverse = (nodes) => {
            for (const node of nodes) {
              // 优先使用id匹配，如果id不存在则使用operationCode匹配
              if (selectedIds.includes(node.id) ||
                  this.localSelectedSurgeries.some(s => s.operationCode === node.operationCode && !selectedIds.includes(s.id))) {
                checkedIds.push(node.id)
              }
              if (node.children && node.children.length > 0) {
                traverse(node.children)
              }
            }
          }
          traverse(this.dataSurgery)
          // 设置树的选中状态
          this.$refs.treeSurgery.setCheckedKeys(checkedIds)

          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      }
    },
    filterNodeSurgery(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    handleRemoveTag(index) {
      const totalSelected = this.localSelectedSurgeries.length;
      if (index < totalSelected) {
        // 删除已有的手术
        const removedSurgery = this.localSelectedSurgeries[index];
        this.localSelectedSurgeries.splice(index, 1);

        // 更新 tree 的选中状态，使用id进行精确匹配
        if (this.$refs.treeSurgery) {
          const node = this.findNodeById(this.dataSurgery, removedSurgery.id);
          if (node) {
            this.$refs.treeSurgery.setChecked(node.id, false);
          }
        }
      } else {
        // 删除新选择的手术
        const newIndex = index - totalSelected;
        const removedSurgery = this.dataForm.choosedSurgery[newIndex];
        this.dataForm.choosedSurgery.splice(newIndex, 1);

        // 更新 tree 的选中状态
        if (this.$refs.treeSurgery && removedSurgery) {
          this.$refs.treeSurgery.setChecked(removedSurgery.id, false);
        }
      }
    },
    findNodeByOperationCode(nodes, operationCode) {
      for (const node of nodes) {
        if (node.operationCode === operationCode) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeByOperationCode(node.children, operationCode)
          if (found) return found
        }
      }
      return null
    },

    // 新增：通过id精确查找节点
    findNodeById(nodes, nodeId) {
      for (const node of nodes) {
        if (node.id === nodeId) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeById(node.children, nodeId)
          if (found) return found
        }
      }
      return null
    },

    // 添加节点到已选择列表
    addToSelectedList(node) {
      const existingInLocal = this.localSelectedSurgeries.some(s => s.id === node.id);
      const existingInNew = this.dataForm.choosedSurgery.some(s => s.id === node.id);

      if (!existingInLocal && !existingInNew) {
        this.dataForm.choosedSurgery.push(node);
      }
    },

    // 从已选择列表中移除节点
    removeFromSelectedList(node) {
      this.dataForm.choosedSurgery = this.dataForm.choosedSurgery.filter(s => s.id !== node.id);
      this.localSelectedSurgeries = this.localSelectedSurgeries.filter(s => s.id !== node.id);
    },

    // 设置树控件节点选中状态
    setTreeNodeChecked(nodeId, checked) {
      if (this.$refs.treeSurgery) {
        this.$refs.treeSurgery.setChecked(nodeId, checked);
      }
    },

    // 获取节点的所有叶子节点ID
    getAllLeafNodeIds(node) {
      const leafIds = [];
      const traverse = (currentNode) => {
        if (this.hasChildren(currentNode)) {
          currentNode.children.forEach(child => traverse(child));
        } else {
          leafIds.push(currentNode.id);
        }
      };
      traverse(node);
      return leafIds;
    },

    // 检查节点是否被选中（基于其叶子节点是否都被选中）
    isNodeSelected(node) {
      if (!this.hasChildren(node)) {
        // 叶子节点：直接检查是否在已选择列表中
        return this.localSelectedSurgeries.some(s => s.id === node.id) ||
               this.dataForm.choosedSurgery.some(s => s.id === node.id);
      } else {
        // 有子节点：检查所有叶子节点是否都被选中
        const leafIds = this.getAllLeafNodeIds(node);
        return leafIds.every(leafId =>
          this.localSelectedSurgeries.some(s => s.id === leafId) ||
          this.dataForm.choosedSurgery.some(s => s.id === leafId)
        );
      }
    },
    async dataFormSubmitHandle() {
      // 检查是否有选中的手术（包括已有的和新选的）
      if (this.choosedSurgeryShow.length === 0) {
        this.$message.error('请选择手术')
        return
      }

      this.loading = true
      try {
        // 合并本地已选和新选的手术
        const allSurgeries = [
          ...this.localSelectedSurgeries,
          ...this.dataForm.choosedSurgery
        ]

        // 构建最终的编码和名称字符串
        const finalOperationCode = allSurgeries.map(item => item.operationCode).join(',')
        const finalSurgeryName = allSurgeries.map(item => item.surgeryName).join(',')

        // 发送回调，包含合并后的手术编码和名称
        this.$emit('callback', {
          operationCode: finalOperationCode,
          surgeryName: finalSurgeryName
        })
        this.visible = false
      } catch (err) {
        console.error('Error in dataFormSubmitHandle:', err)
      } finally {
        this.loading = false
      }
    }
  },
  computed: {
    choosedSurgeryShow() {
      // 合并本地已选和新选的手术名称
      const existingNames = this.localSelectedSurgeries.map(v => v.surgeryName)
      const newNames = this.dataForm.choosedSurgery.map(v => v.surgeryName)
      return [...existingNames, ...newNames]
    },
    choosedSurgeryCode() {
      return this.dataForm.choosedSurgery.length > 0
        ? (this.selectedCode ? this.selectedCode + ',' : '') + this.dataForm.choosedSurgery.map(v => v.operationCode).join(',')
        : this.selectedCode
    }
  }
}
</script>

<style lang="scss" scoped>
.tag-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  min-height: 32px;
  margin-bottom: 5px;
}

.filter-tree-dept {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  overflow-y: auto;
}
</style>