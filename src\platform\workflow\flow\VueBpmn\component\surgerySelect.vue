<template>
  <qz-dialog :title="title" :visible.sync="visible" top="6vh" width="600px" @close="visible = false"
    v-loading="loading">
    <div class="KS-Form">
      <el-form ref="dataForm" :model="dataForm" label-width="80px" size="small">
        <el-form-item label="已选手术" :class="{ 'is-error': choosedSurgeryShow.length === 0 }">
          <div class="tag-container">
            <el-tag v-for="(surgery, index) in choosedSurgeryShow" :key="index" closable @close="handleRemoveTag(index)"
              style="margin: 2px 5px 2px 0;">
              {{ surgery }}
            </el-tag>
            <div v-if="choosedSurgeryShow.length === 0" class="el-form-item__error">
              请选择手术
            </div>
          </div>
        </el-form-item>
        <el-form-item label="输入手术">
          <el-input placeholder="输入关键字过滤手术" v-model="filterTextSurgery" size="small" clearable
            @clear="dataSurgery = []">
            <i @click="initLeftSurgeryTreeData" class="el-input__icon el-icon-search" slot="suffix"
              style="cursor: pointer"></i>
          </el-input>
          <el-tree class="filter-tree-dept" :data="dataSurgery" show-checkbox node-key="id" :props="defaultProps"
            style="max-height: 300px;margin-top: 5px;" :filter-node-method="filterNodeSurgery" ref="treeSurgery"
            :expand-on-click-node="false" :highlight-current="true" @check-change="handleCheckChange">
          </el-tree>
        </el-form-item>
      </el-form>
    </div>
    <template slot="footer">
      <el-button size="small" @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button size="small" @click="dataFormSubmitHandle()" type="primary" v-loading="loading">{{ $t('confirm')
      }}</el-button>
    </template>
  </qz-dialog>
</template>

<script>
/**
 * @name surgery-select (组件名称)
 * @module 组件存放位置
 * @desc 组件描述
 * <AUTHOR>
 * @param {Object} [title]    - 参数说明
 * @param {String} [columns] - 参数说明
 * @example 调用示例
 *  <KS-Form></KS-Form>
 */
import * as api from '../../api'

export default {
  name: 'surgery-select',
  components: {},
  props: {
    selectedName: {
      type: String,
      default: ''
    },
    selectedCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      title: '选择手术',
      filterTextSurgery: '',
      dataSurgery: [],
      defaultProps: {
        children: 'children',
        label: 'surgeryName'
      },
      dataForm: {
        choosedSurgery: []
      },
      localSelectedSurgeries: [] // 用于本地存储已选择的手术
    }
  },
  mounted() {
  },
  methods: {
    init(data) {
      this.visible = true
      this.dataForm.choosedSurgery = []

      // 初始化本地已选择的手术
      if (this.selectedCode && this.selectedName) {
        const codes = this.selectedCode.split(',')
        const names = this.selectedName.split(',')

        this.localSelectedSurgeries = codes.map((code, index) => ({
          id: code,
          operationCode: code,
          surgeryName: names[index]
        }))
      } else {
        this.localSelectedSurgeries = []
      }

      this.$nextTick(() => {
        this.initLeftSurgeryTreeData()
      })
    },
    initData(data) {
      this.$refs.treeSurgery.setCheckedKeys(data.split(','))
    },
    getParams() {
      return this.dataForm.choosedSurgery.map(item => item.pkid).join(',')
    },
    validateForm() {
      return this.$refs.dataForm.validate().then(v => v, e => {
      })
    },
    handleCheckChange(checkedNode, isChecked) {
      const nodeId = checkedNode.id;

      if (isChecked) {
        // 如果节点有子节点，选择所有子节点而不是根节点本身
        if (checkedNode.children && checkedNode.children.length > 0) {
          // 取消根节点的选中状态
          this.$nextTick(() => {
            this.$refs.treeSurgery.setChecked(nodeId, false);
          });

          // 选中所有子节点
          this.selectAllChildren(checkedNode.children);
        } else {
          // 没有子节点的节点，正常选择
          const existingInLocal = this.localSelectedSurgeries.some(s => s.id === nodeId);
          const existingInNew = this.dataForm.choosedSurgery.some(s => s.id === nodeId);
          if (!existingInLocal && !existingInNew) {
            this.dataForm.choosedSurgery.push(checkedNode);
          }
        }
      } else {
        // 取消选择
        if (checkedNode.children && checkedNode.children.length > 0) {
          // 如果是有子节点的根节点，取消所有子节点
          this.unselectAllChildren(checkedNode.children);
        } else {
          // 没有子节点的节点，正常取消选择
          this.dataForm.choosedSurgery = this.dataForm.choosedSurgery.filter(s => s.id !== nodeId);
          this.localSelectedSurgeries = this.localSelectedSurgeries.filter(s => s.id !== nodeId);
        }
      }
    },
    initLeftSurgeryTreeData() {
      if (this.filterTextSurgery) {
        this.loading = true
        api.surgeryList(this.filterTextSurgery).then((res) => {
          this.dataSurgery = res
          // 使用id进行精确匹配，避免同名节点问题
          const selectedIds = this.localSelectedSurgeries.map(s => s.id)
          const checkedIds = []
          const traverse = (nodes) => {
            for (const node of nodes) {
              // 优先使用id匹配，如果id不存在则使用operationCode匹配
              if (selectedIds.includes(node.id) ||
                  this.localSelectedSurgeries.some(s => s.operationCode === node.operationCode && !selectedIds.includes(s.id))) {
                checkedIds.push(node.id)
              }
              if (node.children && node.children.length > 0) {
                traverse(node.children)
              }
            }
          }
          traverse(this.dataSurgery)
          // 设置树的选中状态
          this.$refs.treeSurgery.setCheckedKeys(checkedIds)

          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      }
    },
    filterNodeSurgery(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    handleRemoveTag(index) {
      const totalSelected = this.localSelectedSurgeries.length;
      if (index < totalSelected) {
        // 删除已有的手术
        const removedSurgery = this.localSelectedSurgeries[index];
        this.localSelectedSurgeries.splice(index, 1);

        // 更新 tree 的选中状态，使用id进行精确匹配
        if (this.$refs.treeSurgery) {
          const node = this.findNodeById(this.dataSurgery, removedSurgery.id);
          if (node) {
            this.$refs.treeSurgery.setChecked(node.id, false);
          }
        }
      } else {
        // 删除新选择的手术
        const newIndex = index - totalSelected;
        const removedSurgery = this.dataForm.choosedSurgery[newIndex];
        this.dataForm.choosedSurgery.splice(newIndex, 1);

        // 更新 tree 的选中状态
        if (this.$refs.treeSurgery && removedSurgery) {
          this.$refs.treeSurgery.setChecked(removedSurgery.id, false);
        }
      }
    },
    findNodeByOperationCode(nodes, operationCode) {
      for (const node of nodes) {
        if (node.operationCode === operationCode) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeByOperationCode(node.children, operationCode)
          if (found) return found
        }
      }
      return null
    },

    // 新增：通过id精确查找节点
    findNodeById(nodes, nodeId) {
      for (const node of nodes) {
        if (node.id === nodeId) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeById(node.children, nodeId)
          if (found) return found
        }
      }
      return null
    },

    // 新增：选中所有子节点
    selectAllChildren(children) {
      for (const child of children) {
        // 检查是否已存在
        const existingInLocal = this.localSelectedSurgeries.some(s => s.id === child.id);
        const existingInNew = this.dataForm.choosedSurgery.some(s => s.id === child.id);

        if (!existingInLocal && !existingInNew) {
          // 如果子节点还有子节点，递归选择其子节点，不选择自己
          if (child.children && child.children.length > 0) {
            this.selectAllChildren(child.children);
          } else {
            // 叶子节点，添加到选择列表
            this.dataForm.choosedSurgery.push(child);
          }
        }

        // 在树控件中设置选中状态
        if (this.$refs.treeSurgery) {
          this.$refs.treeSurgery.setChecked(child.id, true);
        }
      }
    },

    // 新增：取消选中所有子节点
    unselectAllChildren(children) {
      for (const child of children) {
        // 从选择列表中移除
        this.dataForm.choosedSurgery = this.dataForm.choosedSurgery.filter(s => s.id !== child.id);
        this.localSelectedSurgeries = this.localSelectedSurgeries.filter(s => s.id !== child.id);

        // 在树控件中取消选中
        if (this.$refs.treeSurgery) {
          this.$refs.treeSurgery.setChecked(child.id, false);
        }

        // 递归处理子节点
        if (child.children && child.children.length > 0) {
          this.unselectAllChildren(child.children);
        }
      }
    },
    async dataFormSubmitHandle() {
      // 检查是否有选中的手术（包括已有的和新选的）
      if (this.choosedSurgeryShow.length === 0) {
        this.$message.error('请选择手术')
        return
      }

      this.loading = true
      try {
        // 合并本地已选和新选的手术
        const allSurgeries = [
          ...this.localSelectedSurgeries,
          ...this.dataForm.choosedSurgery
        ]

        // 构建最终的编码和名称字符串
        const finalOperationCode = allSurgeries.map(item => item.operationCode).join(',')
        const finalSurgeryName = allSurgeries.map(item => item.surgeryName).join(',')

        // 发送回调，包含合并后的手术编码和名称
        this.$emit('callback', {
          operationCode: finalOperationCode,
          surgeryName: finalSurgeryName
        })
        this.visible = false
      } catch (err) {
        console.error('Error in dataFormSubmitHandle:', err)
      } finally {
        this.loading = false
      }
    }
  },
  computed: {
    choosedSurgeryShow() {
      // 合并本地已选和新选的手术名称
      const existingNames = this.localSelectedSurgeries.map(v => v.surgeryName)
      const newNames = this.dataForm.choosedSurgery.map(v => v.surgeryName)
      return [...existingNames, ...newNames]
    },
    choosedSurgeryCode() {
      return this.dataForm.choosedSurgery.length > 0
        ? (this.selectedCode ? this.selectedCode + ',' : '') + this.dataForm.choosedSurgery.map(v => v.operationCode).join(',')
        : this.selectedCode
    }
  }
}
</script>

<style lang="scss" scoped>
.tag-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  min-height: 32px;
  margin-bottom: 5px;
}

.filter-tree-dept {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  overflow-y: auto;
}
</style>